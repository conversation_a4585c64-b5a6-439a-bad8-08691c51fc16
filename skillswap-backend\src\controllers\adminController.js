const { User, Skill, UserOfferedSkill, UserDesiredSkill, Match } = require('../config/database');
const { Op } = require('sequelize');

// Get admin dashboard statistics
const getDashboardStats = async (req, res) => {
  try {
    const totalUsers = await User.count();
    const activeUsers = await User.count({ where: { status: 'active' } });
    const pendingUsers = await User.count({ where: { status: 'pending' } });
    const totalSkills = await Skill.count();
    const pendingSkills = await UserOfferedSkill.count({ where: { status: 'pending' } });
    const totalMatches = await Match.count();
    const activeMatches = await Match.count({ where: { status: 'accepted' } });

    // Recent activity
    const recentUsers = await User.findAll({
      limit: 5,
      order: [['created_at', 'DESC']],
      attributes: ['id', 'name', 'email', 'status', 'created_at']
    });

    const recentMatches = await Match.findAll({
      limit: 5,
      order: [['created_at', 'DESC']],
      include: ['teacher', 'learner']
    });

    res.json({
      success: true,
      data: {
        statistics: {
          totalUsers,
          activeUsers,
          pendingUsers,
          totalSkills,
          pendingSkills,
          totalMatches,
          activeMatches
        },
        recentActivity: {
          recentUsers,
          recentMatches
        }
      }
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching dashboard statistics'
    });
  }
};

// Get all users for admin management
const getAllUsersAdmin = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search = '', 
      status = 'all',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (status !== 'all') {
      whereClause.status = status;
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { username: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows: users } = await User.findAndCountAll({
      where: whereClause,
      include: [
        {
          association: 'offeredSkills',
          include: ['skill']
        },
        {
          association: 'desiredSkills',
          include: ['skill']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalUsers: count,
          hasNext: page * limit < count,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get all users admin error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching users'
    });
  }
};

// Update user status (admin only)
const updateUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['active', 'pending', 'suspended', 'blocked'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Prevent admin from changing their own status
    if (user.id === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot change your own status'
      });
    }

    await user.update({ status });

    res.json({
      success: true,
      message: `User status updated to ${status}`,
      data: { user }
    });
  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating user status'
    });
  }
};

// Get pending skill approvals
const getPendingSkills = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const { count, rows: pendingSkills } = await UserOfferedSkill.findAndCountAll({
      where: { status: 'pending' },
      include: ['user', 'skill'],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'ASC']]
    });

    res.json({
      success: true,
      data: {
        pendingSkills,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalPending: count,
          hasNext: page * limit < count,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get pending skills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching pending skills'
    });
  }
};

// Approve or reject skill
const moderateSkill = async (req, res) => {
  try {
    const { id } = req.params;
    const { action, reason } = req.body; // action: 'approve' or 'reject'

    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Action must be either "approve" or "reject"'
      });
    }

    const offeredSkill = await UserOfferedSkill.findByPk(id, {
      include: ['user', 'skill']
    });

    if (!offeredSkill) {
      return res.status(404).json({
        success: false,
        error: 'Offered skill not found'
      });
    }

    if (offeredSkill.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: 'Can only moderate pending skills'
      });
    }

    const newStatus = action === 'approve' ? 'approved' : 'rejected';
    await offeredSkill.update({ 
      status: newStatus,
      // Could add a reason field to the model if needed
    });

    res.json({
      success: true,
      message: `Skill ${action}d successfully`,
      data: { offeredSkill }
    });
  } catch (error) {
    console.error('Moderate skill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while moderating skill'
    });
  }
};

// Get all matches for admin oversight
const getAllMatches = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status = 'all',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (status !== 'all') {
      whereClause.status = status;
    }

    const { count, rows: matches } = await Match.findAndCountAll({
      where: whereClause,
      include: [
        {
          association: 'teacherSkill',
          include: ['skill', 'user']
        },
        {
          association: 'learnerSkill',
          include: ['skill', 'user']
        },
        {
          association: 'teacher'
        },
        {
          association: 'learner'
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        matches,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalMatches: count,
          hasNext: page * limit < count,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get all matches error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching matches'
    });
  }
};

// Delete user (admin only)
const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Prevent admin from deleting themselves
    if (user.id === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account'
      });
    }

    // Prevent deleting other admins
    if (user.is_admin) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete admin accounts'
      });
    }

    await user.destroy();

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting user'
    });
  }
};

module.exports = {
  getDashboardStats,
  getAllUsersAdmin,
  updateUserStatus,
  getPendingSkills,
  moderateSkill,
  getAllMatches,
  deleteUser
};
