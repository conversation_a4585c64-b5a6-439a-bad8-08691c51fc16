const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Skill = sequelize.define('Skill', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 100]
      }
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        isIn: [['Technology', 'Business', 'Creative', 'Language', 'Health', 'Education', 'Sports', 'Music', 'Cooking', 'Other']]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    requires_certification: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    }
  }, {
    tableName: 'skills',
    indexes: [
      {
        fields: ['category']
      },
      {
        fields: ['name']
      }
    ]
  });

  return Skill;
};
