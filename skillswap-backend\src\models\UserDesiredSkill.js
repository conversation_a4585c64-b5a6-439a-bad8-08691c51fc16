const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const UserDesiredSkill = sequelize.define('UserDesiredSkill', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    skill_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'skills',
        key: 'id'
      }
    },
    interest_level: {
      type: DataTypes.ENUM('low', 'medium', 'high'),
      allowNull: false,
      validate: {
        isIn: [['low', 'medium', 'high']]
      }
    },
    current_knowledge: {
      type: DataTypes.ENUM('none', 'beginner', 'some'),
      allowNull: true,
      validate: {
        isIn: [['none', 'beginner', 'some']]
      }
    },
    learning_goal: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active'
    }
  }, {
    tableName: 'user_desired_skills',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['skill_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['interest_level']
      },
      {
        unique: true,
        fields: ['user_id', 'skill_id']
      }
    ]
  });

  return UserDesiredSkill;
};
