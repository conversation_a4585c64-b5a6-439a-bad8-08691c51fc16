const { User, UserOfferedSkill } = require('../config/database');
const path = require('path');
const fs = require('fs');

// Upload avatar
const uploadAvatar = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    const userId = req.user.id;
    const avatarPath = `/uploads/avatars/${req.file.filename}`;

    // Update user's avatar in database
    const user = await User.findByPk(userId);
    if (!user) {
      // Clean up uploaded file if user not found
      fs.unlinkSync(req.file.path);
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Delete old avatar file if it exists
    if (user.avatar && user.avatar.startsWith('/uploads/avatars/')) {
      const oldAvatarPath = path.join(__dirname, '../../', user.avatar);
      if (fs.existsSync(oldAvatarPath)) {
        fs.unlinkSync(oldAvatarPath);
      }
    }

    // Update user with new avatar path
    await user.update({ avatar: avatarPath });

    res.json({
      success: true,
      message: 'Avatar uploaded successfully',
      data: {
        avatar: avatarPath,
        user: user
      }
    });
  } catch (error) {
    // Clean up uploaded file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    console.error('Upload avatar error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while uploading avatar'
    });
  }
};

// Upload certification for offered skill
const uploadCertification = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    const { skillId } = req.params;
    const userId = req.user.id;
    const certificationPath = `/uploads/certifications/${req.file.filename}`;

    // Find the offered skill
    const offeredSkill = await UserOfferedSkill.findOne({
      where: {
        id: skillId,
        user_id: userId
      },
      include: ['skill', 'user']
    });

    if (!offeredSkill) {
      // Clean up uploaded file if skill not found
      fs.unlinkSync(req.file.path);
      return res.status(404).json({
        success: false,
        error: 'Offered skill not found or access denied'
      });
    }

    // Delete old certification file if it exists
    if (offeredSkill.certification_file && offeredSkill.certification_file.startsWith('/uploads/certifications/')) {
      const oldCertPath = path.join(__dirname, '../../', offeredSkill.certification_file);
      if (fs.existsSync(oldCertPath)) {
        fs.unlinkSync(oldCertPath);
      }
    }

    // Update offered skill with certification
    await offeredSkill.update({
      certification_file: certificationPath,
      has_certification: true
    });

    res.json({
      success: true,
      message: 'Certification uploaded successfully',
      data: {
        certification: certificationPath,
        offeredSkill: offeredSkill
      }
    });
  } catch (error) {
    // Clean up uploaded file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    console.error('Upload certification error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while uploading certification'
    });
  }
};

// Delete file
const deleteFile = async (req, res) => {
  try {
    const { type, filename } = req.params;
    const userId = req.user.id;

    if (!['avatars', 'certifications'].includes(type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid file type'
      });
    }

    const filePath = path.join(__dirname, '../../uploads', type, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'File not found'
      });
    }

    // Verify ownership
    if (type === 'avatars') {
      const user = await User.findOne({
        where: {
          id: userId,
          avatar: `/uploads/avatars/${filename}`
        }
      });

      if (!user) {
        return res.status(403).json({
          success: false,
          error: 'Access denied or file not associated with your account'
        });
      }

      // Remove avatar reference from user
      await user.update({ avatar: null });
    } else if (type === 'certifications') {
      const offeredSkill = await UserOfferedSkill.findOne({
        where: {
          user_id: userId,
          certification_file: `/uploads/certifications/${filename}`
        }
      });

      if (!offeredSkill) {
        return res.status(403).json({
          success: false,
          error: 'Access denied or file not associated with your account'
        });
      }

      // Remove certification reference from offered skill
      await offeredSkill.update({
        certification_file: null,
        has_certification: false
      });
    }

    // Delete the file
    fs.unlinkSync(filePath);

    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting file'
    });
  }
};

module.exports = {
  uploadAvatar,
  uploadCertification,
  deleteFile
};
