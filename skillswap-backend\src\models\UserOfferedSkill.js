const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const UserOfferedSkill = sequelize.define('UserOfferedSkill', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    skill_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'skills',
        key: 'id'
      }
    },
    proficiency_level: {
      type: DataTypes.ENUM('intermediate', 'advanced', 'expert'),
      allowNull: false,
      validate: {
        isIn: [['intermediate', 'advanced', 'expert']]
      }
    },
    years_experience: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0,
        max: 50
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    has_certification: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    certification_file: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('approved', 'pending', 'rejected'),
      allowNull: false,
      defaultValue: 'pending'
    }
  }, {
    tableName: 'user_offered_skills',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['skill_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['proficiency_level']
      },
      {
        unique: true,
        fields: ['user_id', 'skill_id']
      }
    ]
  });

  return UserOfferedSkill;
};
