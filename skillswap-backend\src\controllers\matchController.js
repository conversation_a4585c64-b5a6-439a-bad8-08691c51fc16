const { Match, UserOfferedSkill, UserDesired<PERSON><PERSON>, <PERSON>r, <PERSON>ll } = require('../config/database');
const { Op } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     Match:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         teacher_skill_id:
 *           type: integer
 *         learner_skill_id:
 *           type: integer
 *         teacher_id:
 *           type: integer
 *         learner_id:
 *           type: integer
 *         status:
 *           type: string
 *           enum: [pending, accepted, rejected, completed, cancelled]
 *         scheduled_date:
 *           type: string
 *           format: date-time
 *         duration_minutes:
 *           type: integer
 *         notes:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

// Get user's matches
const getUserMatches = async (req, res) => {
  try {
    const userId = req.user.id;
    const { status = 'all', type = 'all' } = req.query;

    let whereClause = {};
    
    if (type === 'teaching') {
      whereClause.teacher_id = userId;
    } else if (type === 'learning') {
      whereClause.learner_id = userId;
    } else {
      whereClause[Op.or] = [
        { teacher_id: userId },
        { learner_id: userId }
      ];
    }

    if (status !== 'all') {
      whereClause.status = status;
    }

    const matches = await Match.findAll({
      where: whereClause,
      include: [
        {
          association: 'teacherSkill',
          include: ['skill', 'user']
        },
        {
          association: 'learnerSkill',
          include: ['skill', 'user']
        },
        {
          association: 'teacher'
        },
        {
          association: 'learner'
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: { matches }
    });
  } catch (error) {
    console.error('Get user matches error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching matches'
    });
  }
};

// Find potential matches for a user
const findPotentialMatches = async (req, res) => {
  try {
    const userId = req.user.id;
    const { skill_id, type = 'learning' } = req.query;

    let potentialMatches = [];

    if (type === 'learning') {
      // User wants to learn, find teachers
      const whereClause = { status: 'approved' };
      if (skill_id) {
        whereClause.skill_id = skill_id;
      }

      potentialMatches = await UserOfferedSkill.findAll({
        where: {
          ...whereClause,
          user_id: { [Op.ne]: userId } // Exclude current user
        },
        include: [
          'skill',
          {
            association: 'user',
            where: { status: 'active' }
          }
        ],
        order: [['created_at', 'DESC']],
        limit: 20
      });
    } else {
      // User wants to teach, find learners
      const whereClause = { status: 'active' };
      if (skill_id) {
        whereClause.skill_id = skill_id;
      }

      potentialMatches = await UserDesiredSkill.findAll({
        where: {
          ...whereClause,
          user_id: { [Op.ne]: userId } // Exclude current user
        },
        include: [
          'skill',
          {
            association: 'user',
            where: { status: 'active' }
          }
        ],
        order: [['created_at', 'DESC']],
        limit: 20
      });
    }

    res.json({
      success: true,
      data: { potentialMatches, type }
    });
  } catch (error) {
    console.error('Find potential matches error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while finding matches'
    });
  }
};

// Create a new match request
const createMatch = async (req, res) => {
  try {
    const { teacher_skill_id, learner_skill_id, scheduled_date, duration_minutes, notes } = req.body;
    const userId = req.user.id;

    // Validate the skills exist and get teacher/learner IDs
    const teacherSkill = await UserOfferedSkill.findByPk(teacher_skill_id, {
      include: ['user', 'skill']
    });
    
    const learnerSkill = await UserDesiredSkill.findByPk(learner_skill_id, {
      include: ['user', 'skill']
    });

    if (!teacherSkill || !learnerSkill) {
      return res.status(404).json({
        success: false,
        error: 'Teacher skill or learner skill not found'
      });
    }

    // Ensure skills are for the same skill
    if (teacherSkill.skill_id !== learnerSkill.skill_id) {
      return res.status(400).json({
        success: false,
        error: 'Teacher and learner skills must be for the same skill'
      });
    }

    // Ensure user is either the teacher or learner
    if (userId !== teacherSkill.user_id && userId !== learnerSkill.user_id) {
      return res.status(403).json({
        success: false,
        error: 'You can only create matches for your own skills'
      });
    }

    // Check if match already exists
    const existingMatch = await Match.findOne({
      where: {
        teacher_skill_id,
        learner_skill_id,
        status: { [Op.in]: ['pending', 'accepted'] }
      }
    });

    if (existingMatch) {
      return res.status(400).json({
        success: false,
        error: 'A match request already exists for these skills'
      });
    }

    const match = await Match.create({
      teacher_skill_id,
      learner_skill_id,
      teacher_id: teacherSkill.user_id,
      learner_id: learnerSkill.user_id,
      scheduled_date: scheduled_date || null,
      duration_minutes: duration_minutes || null,
      notes: notes || null,
      status: 'pending'
    });

    // Fetch the complete match with associations
    const completeMatch = await Match.findByPk(match.id, {
      include: [
        {
          association: 'teacherSkill',
          include: ['skill', 'user']
        },
        {
          association: 'learnerSkill',
          include: ['skill', 'user']
        },
        {
          association: 'teacher'
        },
        {
          association: 'learner'
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Match request created successfully',
      data: { match: completeMatch }
    });
  } catch (error) {
    console.error('Create match error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating match'
    });
  }
};

// Update match status
const updateMatchStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, scheduled_date, duration_minutes, notes } = req.body;
    const userId = req.user.id;

    const match = await Match.findByPk(id, {
      include: ['teacher', 'learner']
    });

    if (!match) {
      return res.status(404).json({
        success: false,
        error: 'Match not found'
      });
    }

    // Check authorization - only teacher or learner can update
    if (match.teacher_id !== userId && match.learner_id !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied. You can only update your own matches.'
      });
    }

    // Validate status transitions
    const validTransitions = {
      'pending': ['accepted', 'rejected', 'cancelled'],
      'accepted': ['completed', 'cancelled'],
      'rejected': [],
      'completed': [],
      'cancelled': []
    };

    if (status && !validTransitions[match.status].includes(status)) {
      return res.status(400).json({
        success: false,
        error: `Cannot change status from ${match.status} to ${status}`
      });
    }

    // Update match
    const updateData = {};
    if (status) updateData.status = status;
    if (scheduled_date !== undefined) updateData.scheduled_date = scheduled_date;
    if (duration_minutes !== undefined) updateData.duration_minutes = duration_minutes;
    if (notes !== undefined) updateData.notes = notes;

    await match.update(updateData);

    // Fetch updated match with associations
    const updatedMatch = await Match.findByPk(id, {
      include: [
        {
          association: 'teacherSkill',
          include: ['skill', 'user']
        },
        {
          association: 'learnerSkill',
          include: ['skill', 'user']
        },
        {
          association: 'teacher'
        },
        {
          association: 'learner'
        }
      ]
    });

    res.json({
      success: true,
      message: 'Match updated successfully',
      data: { match: updatedMatch }
    });
  } catch (error) {
    console.error('Update match status error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating match'
    });
  }
};

// Delete/cancel match
const deleteMatch = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const match = await Match.findByPk(id);
    if (!match) {
      return res.status(404).json({
        success: false,
        error: 'Match not found'
      });
    }

    // Check authorization
    if (match.teacher_id !== userId && match.learner_id !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied. You can only delete your own matches.'
      });
    }

    // Can only delete pending matches, others should be cancelled
    if (match.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: 'Can only delete pending matches. Use status update to cancel accepted matches.'
      });
    }

    await match.destroy();

    res.json({
      success: true,
      message: 'Match deleted successfully'
    });
  } catch (error) {
    console.error('Delete match error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting match'
    });
  }
};

module.exports = {
  getUserMatches,
  findPotentialMatches,
  createMatch,
  updateMatchStatus,
  deleteMatch
};
