// API service for SkillSwap backend integration
const API_BASE_URL = 'http://localhost:5000/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth headers
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  // Helper method to handle API responses
  async handleResponse(response) {
    console.log('API Response status:', response.status);
    console.log('API Response headers:', response.headers);

    const data = await response.json();
    console.log('API Response data:', data);

    if (!response.ok) {
      console.error('API Error:', data.error || `HTTP error! status: ${response.status}`);
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  }

  // Authentication endpoints
  async register(userData) {
    console.log('Register request:', userData);
    try {
      const response = await fetch(`${this.baseURL}/auth/register`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(userData)
      });

      console.log('Register response received');
      return this.handleResponse(response);
    } catch (error) {
      console.error('Register fetch error:', error);
      throw error;
    }
  }

  async login(credentials) {
    console.log('Login request:', credentials);
    try {
      const response = await fetch(`${this.baseURL}/auth/login`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(credentials)
      });

      console.log('Login response received');
      return this.handleResponse(response);
    } catch (error) {
      console.error('Login fetch error:', error);
      throw error;
    }
  }

  async logout() {
    const response = await fetch(`${this.baseURL}/auth/logout`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async getCurrentUser() {
    const response = await fetch(`${this.baseURL}/auth/me`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  // User endpoints
  async getUsers(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`${this.baseURL}/users?${queryString}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async getUserById(id) {
    const response = await fetch(`${this.baseURL}/users/${id}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async updateUserProfile(id, userData) {
    const response = await fetch(`${this.baseURL}/users/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(userData)
    });
    
    return this.handleResponse(response);
  }

  // Skills endpoints
  async getSkills(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`${this.baseURL}/skills?${queryString}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async getSkillCategories() {
    const response = await fetch(`${this.baseURL}/skills/categories`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async searchSkills(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`${this.baseURL}/skills/search?${queryString}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  // User skills endpoints
  async getUserOfferedSkills(userId) {
    const response = await fetch(`${this.baseURL}/users/${userId}/offered-skills`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async addOfferedSkill(userId, skillData) {
    const response = await fetch(`${this.baseURL}/users/${userId}/offered-skills`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(skillData)
    });
    
    return this.handleResponse(response);
  }

  async getUserDesiredSkills(userId) {
    const response = await fetch(`${this.baseURL}/users/${userId}/desired-skills`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async addDesiredSkill(userId, skillData) {
    const response = await fetch(`${this.baseURL}/users/${userId}/desired-skills`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(skillData)
    });
    
    return this.handleResponse(response);
  }

  // Matches endpoints
  async getMatches(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`${this.baseURL}/matches?${queryString}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async findPotentialMatches(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`${this.baseURL}/matches/potential?${queryString}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async createMatch(matchData) {
    const response = await fetch(`${this.baseURL}/matches`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(matchData)
    });
    
    return this.handleResponse(response);
  }

  async updateMatch(matchId, updateData) {
    const response = await fetch(`${this.baseURL}/matches/${matchId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateData)
    });
    
    return this.handleResponse(response);
  }

  // File upload endpoints
  async uploadAvatar(file) {
    const formData = new FormData();
    formData.append('avatar', file);

    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseURL}/upload/avatar`, {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: formData
    });
    
    return this.handleResponse(response);
  }

  async uploadCertification(skillId, file) {
    const formData = new FormData();
    formData.append('certification', file);

    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseURL}/upload/certification/${skillId}`, {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: formData
    });
    
    return this.handleResponse(response);
  }

  // Admin endpoints
  async getAdminDashboard() {
    const response = await fetch(`${this.baseURL}/admin/dashboard`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async updateUserStatus(userId, status) {
    const response = await fetch(`${this.baseURL}/admin/users/${userId}/status`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status })
    });
    
    return this.handleResponse(response);
  }

  async getPendingSkills(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`${this.baseURL}/admin/skills/pending?${queryString}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async moderateSkill(skillId, action, reason = '') {
    const response = await fetch(`${this.baseURL}/admin/skills/${skillId}/moderate`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ action, reason })
    });
    
    return this.handleResponse(response);
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;
