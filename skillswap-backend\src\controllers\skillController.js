const { Skill, UserOfferedSkill, UserDesiredSkill, User } = require('../config/database');
const { Op } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     Skill:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         category:
 *           type: string
 *         description:
 *           type: string
 *         requires_certification:
 *           type: boolean
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

// Get all skills with search and filtering
const getAllSkills = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search = '', 
      category = '',
      sortBy = 'name',
      sortOrder = 'ASC'
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    if (category && category !== 'all') {
      whereClause.category = category;
    }

    const { count, rows: skills } = await Skill.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]]
    });

    res.json({
      success: true,
      data: {
        skills,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalSkills: count,
          hasNext: page * limit < count,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get all skills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching skills'
    });
  }
};

// Get skill by ID with user statistics
const getSkillById = async (req, res) => {
  try {
    const { id } = req.params;

    const skill = await Skill.findByPk(id);
    if (!skill) {
      return res.status(404).json({
        success: false,
        error: 'Skill not found'
      });
    }

    // Get statistics
    const teachersCount = await UserOfferedSkill.count({
      where: { skill_id: id, status: 'approved' }
    });

    const learnersCount = await UserDesiredSkill.count({
      where: { skill_id: id, status: 'active' }
    });

    // Get sample teachers
    const teachers = await UserOfferedSkill.findAll({
      where: { skill_id: id, status: 'approved' },
      include: ['user'],
      limit: 5,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        skill,
        statistics: {
          teachersCount,
          learnersCount
        },
        sampleTeachers: teachers
      }
    });
  } catch (error) {
    console.error('Get skill by ID error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching skill'
    });
  }
};

// Get skill categories with counts
const getSkillCategories = async (req, res) => {
  try {
    const categories = await Skill.findAll({
      attributes: [
        'category',
        [Skill.sequelize.fn('COUNT', Skill.sequelize.col('id')), 'count']
      ],
      group: ['category'],
      order: [['category', 'ASC']]
    });

    res.json({
      success: true,
      data: { categories }
    });
  } catch (error) {
    console.error('Get skill categories error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching categories'
    });
  }
};

// Create new skill (admin only)
const createSkill = async (req, res) => {
  try {
    const { name, category, description, requires_certification } = req.body;

    // Check if skill already exists
    const existingSkill = await Skill.findOne({ where: { name } });
    if (existingSkill) {
      return res.status(400).json({
        success: false,
        error: 'Skill with this name already exists'
      });
    }

    const skill = await Skill.create({
      name,
      category,
      description,
      requires_certification: requires_certification || false
    });

    res.status(201).json({
      success: true,
      message: 'Skill created successfully',
      data: { skill }
    });
  } catch (error) {
    console.error('Create skill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating skill'
    });
  }
};

// Update skill (admin only)
const updateSkill = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, category, description, requires_certification } = req.body;

    const skill = await Skill.findByPk(id);
    if (!skill) {
      return res.status(404).json({
        success: false,
        error: 'Skill not found'
      });
    }

    // Check if name is being changed and if it conflicts
    if (name && name !== skill.name) {
      const existingSkill = await Skill.findOne({ where: { name } });
      if (existingSkill) {
        return res.status(400).json({
          success: false,
          error: 'Skill with this name already exists'
        });
      }
    }

    await skill.update({
      name: name || skill.name,
      category: category || skill.category,
      description: description !== undefined ? description : skill.description,
      requires_certification: requires_certification !== undefined ? requires_certification : skill.requires_certification
    });

    res.json({
      success: true,
      message: 'Skill updated successfully',
      data: { skill }
    });
  } catch (error) {
    console.error('Update skill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating skill'
    });
  }
};

// Delete skill (admin only)
const deleteSkill = async (req, res) => {
  try {
    const { id } = req.params;

    const skill = await Skill.findByPk(id);
    if (!skill) {
      return res.status(404).json({
        success: false,
        error: 'Skill not found'
      });
    }

    // Check if skill is being used
    const offeredCount = await UserOfferedSkill.count({ where: { skill_id: id } });
    const desiredCount = await UserDesiredSkill.count({ where: { skill_id: id } });

    if (offeredCount > 0 || desiredCount > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete skill that is being used by users'
      });
    }

    await skill.destroy();

    res.json({
      success: true,
      message: 'Skill deleted successfully'
    });
  } catch (error) {
    console.error('Delete skill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting skill'
    });
  }
};

// Search skills with advanced filtering
const searchSkills = async (req, res) => {
  try {
    const { 
      q = '', 
      category = '', 
      hasTeachers = false,
      hasLearners = false,
      limit = 10 
    } = req.query;

    let whereClause = {};
    let havingClause = {};

    if (q) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${q}%` } },
        { description: { [Op.like]: `%${q}%` } }
      ];
    }

    if (category && category !== 'all') {
      whereClause.category = category;
    }

    const skills = await Skill.findAll({
      where: whereClause,
      include: [
        {
          model: UserOfferedSkill,
          as: 'offeredBy',
          attributes: [],
          where: hasTeachers ? { status: 'approved' } : undefined,
          required: hasTeachers
        },
        {
          model: UserDesiredSkill,
          as: 'desiredBy',
          attributes: [],
          where: hasLearners ? { status: 'active' } : undefined,
          required: hasLearners
        }
      ],
      attributes: [
        'id',
        'name',
        'category',
        'description',
        'requires_certification',
        [Skill.sequelize.fn('COUNT', Skill.sequelize.fn('DISTINCT', Skill.sequelize.col('offeredBy.id'))), 'teachersCount'],
        [Skill.sequelize.fn('COUNT', Skill.sequelize.fn('DISTINCT', Skill.sequelize.col('desiredBy.id'))), 'learnersCount']
      ],
      group: ['Skill.id'],
      limit: parseInt(limit),
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: { skills }
    });
  } catch (error) {
    console.error('Search skills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while searching skills'
    });
  }
};

module.exports = {
  getAllSkills,
  getSkillById,
  getSkillCategories,
  createSkill,
  updateSkill,
  deleteSkill,
  searchSkills
};
