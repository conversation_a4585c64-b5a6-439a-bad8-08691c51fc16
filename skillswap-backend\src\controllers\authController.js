const { User } = require('../config/database');
const { generateToken } = require('../config/auth');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: User ID
 *         username:
 *           type: string
 *           description: Unique username
 *         email:
 *           type: string
 *           description: User email address
 *         name:
 *           type: string
 *           description: Full name
 *         avatar:
 *           type: string
 *           description: Avatar image URL
 *         bio:
 *           type: string
 *           description: User biography
 *         current_status:
 *           type: string
 *           description: Current employment/education status
 *         state:
 *           type: string
 *           description: State/province
 *         country:
 *           type: string
 *           description: Country
 *         education_level:
 *           type: string
 *           description: Highest education level
 *         university_name:
 *           type: string
 *           description: University/institution name
 *         status:
 *           type: string
 *           enum: [active, pending, suspended, blocked]
 *           description: Account status
 *         is_admin:
 *           type: boolean
 *           description: Admin privileges flag
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Account creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     AuthResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               $ref: '#/components/schemas/User'
 *             token:
 *               type: string
 *               description: JWT authentication token
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         error:
 *           type: string
 *           description: Error message
 */

const register = async (req, res) => {
  try {
    const {
      username,
      email,
      password,
      name,
      bio,
      current_status,
      state,
      country,
      education_level,
      university_name
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [{ email }, { username }]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email or username already exists'
      });
    }

    // Create user
    const user = await User.create({
      username,
      email,
      password,
      name,
      bio,
      current_status,
      state,
      country,
      education_level,
      university_name,
      status: 'active' // Auto-approve for now, can be changed to 'pending' for admin approval
    });

    // Generate token
    const token = generateToken({ id: user.id, email: user.email });

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user,
        token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during registration'
    });
  }
};

const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findOne({ 
      where: { email },
      attributes: { include: ['password'] }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password'
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password'
      });
    }

    // Check user status
    if (user.status === 'blocked') {
      return res.status(403).json({
        success: false,
        error: 'Your account has been blocked. Please contact support.'
      });
    }

    if (user.status === 'suspended') {
      return res.status(403).json({
        success: false,
        error: 'Your account has been suspended. Please contact support.'
      });
    }

    if (user.status === 'pending') {
      return res.status(403).json({
        success: false,
        error: 'Your account is pending approval. Please wait for admin approval.'
      });
    }

    // Generate token
    const token = generateToken({ id: user.id, email: user.email });

    // Remove password from response
    const userResponse = user.toJSON();

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during login'
    });
  }
};

const getMe = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      include: [
        {
          association: 'offeredSkills',
          include: ['skill']
        },
        {
          association: 'desiredSkills',
          include: ['skill']
        }
      ]
    });

    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    console.error('Get me error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

const logout = async (req, res) => {
  try {
    // In a stateless JWT system, logout is handled client-side
    // by removing the token from storage
    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during logout'
    });
  }
};

module.exports = {
  register,
  login,
  getMe,
  logout
};
