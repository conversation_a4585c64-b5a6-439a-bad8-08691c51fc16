{"name": "skillswap-backend", "version": "1.0.0", "description": "Backend API for SkillSwap platform - connecting people who want to learn with those who want to teach", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo:all"}, "keywords": ["skillswap", "skill-sharing", "education", "learning", "teaching", "api", "nodejs", "express"], "author": "SkillSwap Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4", "express-validator": "^7.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=16.0.0"}}