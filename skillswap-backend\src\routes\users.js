const express = require('express');
const router = express.Router();

const {
  getAllUsers,
  getUserById,
  updateUserProfile,
  getUserOfferedSkills,
  addOfferedSkill,
  getUserDesiredSkills,
  addDesiredSkill
} = require('../controllers/userController');

const { authenticate, optionalAuth } = require('../middleware/auth');
const { validate, updateProfileSchema, offeredSkillSchema, desiredSkillSchema } = require('../middleware/validation');

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management endpoints
 */

/**
 * @swagger
 * /users:
 *   get:
 *     summary: Get all users with pagination and search
 *     tags: [Users]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, pending, suspended, blocked, all]
 *           default: active
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/', optionalAuth, getAllUsers);

/**
 * @swagger
 * /users/{id}:
 *   get:
 *     summary: Get user by ID
 *     tags: [Users]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/:id', optionalAuth, getUserById);

/**
 * @swagger
 * /users/{id}:
 *   put:
 *     summary: Update user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               bio:
 *                 type: string
 *               current_status:
 *                 type: string
 *               state:
 *                 type: string
 *               country:
 *                 type: string
 *               education_level:
 *                 type: string
 *               university_name:
 *                 type: string
 *               avatar:
 *                 type: string
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       403:
 *         description: Access denied
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.put('/:id', authenticate, validate(updateProfileSchema), updateUserProfile);

/**
 * @swagger
 * /users/{id}/offered-skills:
 *   get:
 *     summary: Get user's offered skills
 *     tags: [Users]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Offered skills retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/:id/offered-skills', getUserOfferedSkills);

/**
 * @swagger
 * /users/{id}/offered-skills:
 *   post:
 *     summary: Add offered skill
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - skill_id
 *               - proficiency_level
 *             properties:
 *               skill_id:
 *                 type: integer
 *               proficiency_level:
 *                 type: string
 *                 enum: [intermediate, advanced, expert]
 *               years_experience:
 *                 type: integer
 *               description:
 *                 type: string
 *               has_certification:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Skill added successfully
 *       400:
 *         description: Skill already exists or validation error
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.post('/:id/offered-skills', authenticate, validate(offeredSkillSchema), addOfferedSkill);

/**
 * @swagger
 * /users/{id}/desired-skills:
 *   get:
 *     summary: Get user's desired skills
 *     tags: [Users]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Desired skills retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/:id/desired-skills', getUserDesiredSkills);

/**
 * @swagger
 * /users/{id}/desired-skills:
 *   post:
 *     summary: Add desired skill
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - skill_id
 *               - interest_level
 *             properties:
 *               skill_id:
 *                 type: integer
 *               interest_level:
 *                 type: string
 *                 enum: [low, medium, high]
 *               current_knowledge:
 *                 type: string
 *                 enum: [none, beginner, some]
 *               learning_goal:
 *                 type: string
 *     responses:
 *       201:
 *         description: Skill added successfully
 *       400:
 *         description: Skill already exists or validation error
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.post('/:id/desired-skills', authenticate, validate(desiredSkillSchema), addDesiredSkill);

module.exports = router;
