const express = require('express');
const router = express.Router();

const {
  uploadAvatar,
  uploadCertification,
  deleteFile
} = require('../controllers/uploadController');

const { authenticate } = require('../middleware/auth');
const { 
  uploadAvatar: uploadAvatarMiddleware, 
  uploadCertification: uploadCertificationMiddleware,
  handleUploadError 
} = require('../middleware/upload');

/**
 * @swagger
 * tags:
 *   name: Upload
 *   description: File upload endpoints
 */

/**
 * @swagger
 * /upload/avatar:
 *   post:
 *     summary: Upload user avatar
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               avatar:
 *                 type: string
 *                 format: binary
 *                 description: Avatar image file (max 5MB)
 *     responses:
 *       200:
 *         description: Avatar uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     avatar:
 *                       type: string
 *                       description: Avatar file path
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: No file uploaded or invalid file type
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/avatar', authenticate, uploadAvatarMiddleware, handleUploadError, uploadAvatar);

/**
 * @swagger
 * /upload/certification/{skillId}:
 *   post:
 *     summary: Upload certification for offered skill
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: skillId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the offered skill
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               certification:
 *                 type: string
 *                 format: binary
 *                 description: Certification file (image or PDF, max 5MB)
 *     responses:
 *       200:
 *         description: Certification uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     certification:
 *                       type: string
 *                       description: Certification file path
 *                     offeredSkill:
 *                       type: object
 *                       description: Updated offered skill object
 *       400:
 *         description: No file uploaded or invalid file type
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Offered skill not found or access denied
 *       500:
 *         description: Server error
 */
router.post('/certification/:skillId', authenticate, uploadCertificationMiddleware, handleUploadError, uploadCertification);

/**
 * @swagger
 * /upload/{type}/{filename}:
 *   delete:
 *     summary: Delete uploaded file
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [avatars, certifications]
 *         description: Type of file to delete
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of file to delete
 *     responses:
 *       200:
 *         description: File deleted successfully
 *       400:
 *         description: Invalid file type
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied or file not associated with account
 *       404:
 *         description: File not found
 *       500:
 *         description: Server error
 */
router.delete('/:type/:filename', authenticate, deleteFile);

module.exports = router;
