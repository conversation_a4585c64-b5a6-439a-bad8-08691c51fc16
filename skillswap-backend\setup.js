const { sequelize, User, Skill } = require('./src/config/database');
const bcrypt = require('bcryptjs');

const setupDatabase = async () => {
  try {
    console.log('🔄 Setting up database...');

    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection established.');

    // Sync models
    await sequelize.sync({ force: true }); // Use force: true to recreate tables
    console.log('✅ Database models synchronized.');

    // Create admin user
    const adminPassword = await bcrypt.hash('admin12', 12);
    const adminUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password: adminPassword,
      name: 'Administrator',
      bio: 'System Administrator',
      status: 'active',
      is_admin: true
    });
    console.log('✅ Admin user created:', adminUser.email);

    // Create sample skills
    const sampleSkills = [
      { name: 'JavaScript', category: 'Technology', description: 'Programming language for web development' },
      { name: 'React', category: 'Technology', description: 'JavaScript library for building user interfaces' },
      { name: 'Node.js', category: 'Technology', description: 'JavaScript runtime for server-side development' },
      { name: 'Python', category: 'Technology', description: 'High-level programming language' },
      { name: 'Digital Marketing', category: 'Business', description: 'Online marketing strategies and techniques' },
      { name: 'Graphic Design', category: 'Creative', description: 'Visual communication and design' },
      { name: 'Spanish', category: 'Language', description: 'Spanish language learning' },
      { name: 'Guitar', category: 'Music', description: 'Guitar playing and music theory' },
      { name: 'Cooking', category: 'Cooking', description: 'Culinary arts and cooking techniques' },
      { name: 'Yoga', category: 'Health', description: 'Physical and mental wellness practice' }
    ];

    for (const skillData of sampleSkills) {
      await Skill.create(skillData);
    }
    console.log('✅ Sample skills created.');

    console.log('🎉 Database setup completed successfully!');
    console.log('📝 Admin credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin12');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
};

setupDatabase();
