# SkillSwap Backend API

Backend API for the SkillSwap platform - connecting people who want to learn with those who want to teach.

## 🚀 Features

- **User Authentication**: JWT-based authentication with registration and login
- **User Management**: Profile management, user listing, and admin controls
- **Skills Management**: CRUD operations for skills with categories and search
- **Skill Matching**: Algorithm to match users who teach with users who want to learn
- **Admin Panel**: Administrative controls for user and skill moderation
- **File Upload**: Support for user avatars and skill certifications
- **API Documentation**: Comprehensive Swagger/OpenAPI documentation

## 🛠 Technology Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL with Sequelize ORM
- **Authentication**: JWT (JSON Web Tokens)
- **File Upload**: Multer
- **Validation**: Joi
- **Documentation**: Swagger/OpenAPI
- **Security**: Helmet, CORS, Rate Limiting

## 📋 Prerequisites

- Node.js (v16.0.0 or later)
- MySQL (v8.0 or later)
- npm or yarn

## 🔧 Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd skillswap-backend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Environment setup**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   PORT=5000
   NODE_ENV=development
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=skillswap_db
   DB_USER=root
   DB_PASSWORD=your_password
   JWT_SECRET=your_super_secret_jwt_key
   ```

4. **Database setup**:
   ```bash
   # Create database
   mysql -u root -p -e "CREATE DATABASE skillswap_db;"
   
   # Run migrations (if using Sequelize CLI)
   npm run migrate
   
   # Seed initial data (optional)
   npm run seed
   ```

5. **Start the server**:
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## 📚 API Documentation

Once the server is running, visit:
- **API Documentation**: http://localhost:5000/api-docs
- **Health Check**: http://localhost:5000/health

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Users
- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user profile
- `DELETE /api/users/:id` - Delete user (admin only)

### Skills
- `GET /api/skills` - Get all skills
- `POST /api/skills` - Create new skill (admin only)
- `GET /api/skills/:id` - Get skill by ID
- `PUT /api/skills/:id` - Update skill (admin only)
- `DELETE /api/skills/:id` - Delete skill (admin only)

### User Skills
- `GET /api/users/:id/offered-skills` - Get user's offered skills
- `POST /api/users/:id/offered-skills` - Add offered skill
- `GET /api/users/:id/desired-skills` - Get user's desired skills
- `POST /api/users/:id/desired-skills` - Add desired skill

### Matches
- `GET /api/matches` - Get user's matches
- `POST /api/matches` - Create new match
- `PUT /api/matches/:id` - Update match status
- `DELETE /api/matches/:id` - Cancel match

### Admin
- `GET /api/admin/users` - Get all users (admin only)
- `PUT /api/admin/users/:id/status` - Update user status (admin only)
- `GET /api/admin/skills/pending` - Get pending skills (admin only)
- `PUT /api/admin/skills/:id/approve` - Approve skill (admin only)

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 📁 Project Structure

```
skillswap-backend/
├── src/
│   ├── config/          # Database and app configuration
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Custom middleware
│   ├── models/          # Sequelize models
│   ├── routes/          # API routes
│   ├── services/        # Business logic
│   ├── utils/           # Utility functions
│   └── app.js           # Express app configuration
├── uploads/             # File uploads directory
├── .env.example         # Environment variables template
├── server.js            # Server entry point
└── package.json         # Dependencies and scripts
```

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation
- File upload restrictions

## 🚀 Deployment

1. Set `NODE_ENV=production` in your environment
2. Configure production database settings
3. Set up proper JWT secrets
4. Configure file upload storage (consider cloud storage)
5. Set up reverse proxy (nginx recommended)
6. Enable SSL/TLS

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.
