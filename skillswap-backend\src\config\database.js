const { Sequelize } = require('sequelize');
require('dotenv').config();

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME || 'skillswap',
  process.env.DB_USER || 'root',
  process.env.DB_PASSWORD || '',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true
    }
  }
);

// Import models
const User = require('../models/User')(sequelize);
const Skill = require('../models/Skill')(sequelize);
const UserOfferedSkill = require('../models/UserOfferedSkill')(sequelize);
const UserDesiredSkill = require('../models/UserDesiredSkill')(sequelize);
const Match = require('../models/Match')(sequelize);

// Define associations
const defineAssociations = () => {
  // User associations
  User.hasMany(UserOfferedSkill, { 
    foreignKey: 'user_id', 
    as: 'offeredSkills',
    onDelete: 'CASCADE'
  });
  
  User.hasMany(UserDesiredSkill, { 
    foreignKey: 'user_id', 
    as: 'desiredSkills',
    onDelete: 'CASCADE'
  });

  User.hasMany(Match, { 
    foreignKey: 'teacher_id', 
    as: 'teachingMatches',
    onDelete: 'CASCADE'
  });

  User.hasMany(Match, { 
    foreignKey: 'learner_id', 
    as: 'learningMatches',
    onDelete: 'CASCADE'
  });

  // Skill associations
  Skill.hasMany(UserOfferedSkill, { 
    foreignKey: 'skill_id', 
    as: 'offeredBy',
    onDelete: 'CASCADE'
  });
  
  Skill.hasMany(UserDesiredSkill, { 
    foreignKey: 'skill_id', 
    as: 'desiredBy',
    onDelete: 'CASCADE'
  });

  // UserOfferedSkill associations
  UserOfferedSkill.belongsTo(User, { 
    foreignKey: 'user_id', 
    as: 'user'
  });
  
  UserOfferedSkill.belongsTo(Skill, { 
    foreignKey: 'skill_id', 
    as: 'skill'
  });

  UserOfferedSkill.hasMany(Match, { 
    foreignKey: 'teacher_skill_id', 
    as: 'matches',
    onDelete: 'CASCADE'
  });

  // UserDesiredSkill associations
  UserDesiredSkill.belongsTo(User, { 
    foreignKey: 'user_id', 
    as: 'user'
  });
  
  UserDesiredSkill.belongsTo(Skill, { 
    foreignKey: 'skill_id', 
    as: 'skill'
  });

  UserDesiredSkill.hasMany(Match, { 
    foreignKey: 'learner_skill_id', 
    as: 'matches',
    onDelete: 'CASCADE'
  });

  // Match associations
  Match.belongsTo(UserOfferedSkill, { 
    foreignKey: 'teacher_skill_id', 
    as: 'teacherSkill'
  });
  
  Match.belongsTo(UserDesiredSkill, { 
    foreignKey: 'learner_skill_id', 
    as: 'learnerSkill'
  });

  Match.belongsTo(User, { 
    foreignKey: 'teacher_id', 
    as: 'teacher'
  });

  Match.belongsTo(User, { 
    foreignKey: 'learner_id', 
    as: 'learner'
  });
};

// Initialize associations
defineAssociations();

// Export models and sequelize instance
module.exports = {
  sequelize,
  User,
  Skill,
  UserOfferedSkill,
  UserDesiredSkill,
  Match
};
