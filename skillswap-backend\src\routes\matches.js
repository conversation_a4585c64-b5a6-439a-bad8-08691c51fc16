const express = require('express');
const router = express.Router();

const {
  getUserMatches,
  findPotentialMatches,
  createMatch,
  updateMatchStatus,
  deleteMatch
} = require('../controllers/matchController');

const { authenticate } = require('../middleware/auth');
const { validate, matchSchema } = require('../middleware/validation');

/**
 * @swagger
 * tags:
 *   name: Matches
 *   description: Skill matching and exchange management endpoints
 */

/**
 * @swagger
 * /matches:
 *   get:
 *     summary: Get user's matches
 *     tags: [Matches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [all, pending, accepted, rejected, completed, cancelled]
 *           default: all
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [all, teaching, learning]
 *           default: all
 *     responses:
 *       200:
 *         description: Matches retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authenticate, getUserMatches);

/**
 * @swagger
 * /matches/potential:
 *   get:
 *     summary: Find potential matches for user
 *     tags: [Matches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: skill_id
 *         schema:
 *           type: integer
 *         description: Filter by specific skill
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [learning, teaching]
 *           default: learning
 *         description: Whether user wants to learn or teach
 *     responses:
 *       200:
 *         description: Potential matches found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/potential', authenticate, findPotentialMatches);

/**
 * @swagger
 * /matches:
 *   post:
 *     summary: Create a new match request
 *     tags: [Matches]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - teacher_skill_id
 *               - learner_skill_id
 *             properties:
 *               teacher_skill_id:
 *                 type: integer
 *                 description: ID of the offered skill
 *               learner_skill_id:
 *                 type: integer
 *                 description: ID of the desired skill
 *               scheduled_date:
 *                 type: string
 *                 format: date-time
 *               duration_minutes:
 *                 type: integer
 *                 minimum: 15
 *                 maximum: 480
 *               notes:
 *                 type: string
 *                 maxLength: 1000
 *     responses:
 *       201:
 *         description: Match request created successfully
 *       400:
 *         description: Validation error or match already exists
 *       403:
 *         description: Access denied
 *       404:
 *         description: Skills not found
 *       500:
 *         description: Server error
 */
router.post('/', authenticate, validate(matchSchema), createMatch);

/**
 * @swagger
 * /matches/{id}:
 *   put:
 *     summary: Update match status and details
 *     tags: [Matches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [accepted, rejected, completed, cancelled]
 *               scheduled_date:
 *                 type: string
 *                 format: date-time
 *               duration_minutes:
 *                 type: integer
 *                 minimum: 15
 *                 maximum: 480
 *               notes:
 *                 type: string
 *                 maxLength: 1000
 *     responses:
 *       200:
 *         description: Match updated successfully
 *       400:
 *         description: Invalid status transition
 *       403:
 *         description: Access denied
 *       404:
 *         description: Match not found
 *       500:
 *         description: Server error
 */
router.put('/:id', authenticate, updateMatchStatus);

/**
 * @swagger
 * /matches/{id}:
 *   delete:
 *     summary: Delete/cancel match
 *     tags: [Matches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Match deleted successfully
 *       400:
 *         description: Can only delete pending matches
 *       403:
 *         description: Access denied
 *       404:
 *         description: Match not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', authenticate, deleteMatch);

module.exports = router;
