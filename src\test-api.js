// Simple test to verify API connection
const testAPI = async () => {
  try {
    console.log('Testing API connection...');
    
    // Test admin login
    const response = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin12'
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    
    const data = await response.json();
    console.log('Response data:', data);
    
    if (data.success) {
      console.log('✅ Admin login successful!');
      console.log('User:', data.data.user);
      console.log('Token:', data.data.token);
    } else {
      console.log('❌ Admin login failed:', data.error);
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error);
  }
};

// Test registration
const testRegistration = async () => {
  try {
    console.log('Testing registration...');
    
    const response = await fetch('http://localhost:5000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'testuser123',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      })
    });
    
    console.log('Registration response status:', response.status);
    
    const data = await response.json();
    console.log('Registration response data:', data);
    
    if (data.success) {
      console.log('✅ Registration successful!');
    } else {
      console.log('❌ Registration failed:', data.error);
    }
    
  } catch (error) {
    console.error('❌ Registration test failed:', error);
  }
};

// Run tests
console.log('Starting API tests...');
testAPI();
setTimeout(() => testRegistration(), 2000);
