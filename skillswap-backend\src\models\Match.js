const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Match = sequelize.define('Match', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    teacher_skill_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'user_offered_skills',
        key: 'id'
      }
    },
    learner_skill_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'user_desired_skills',
        key: 'id'
      }
    },
    teacher_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    learner_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'accepted', 'rejected', 'completed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending'
    },
    scheduled_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    duration_minutes: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 15,
        max: 480 // 8 hours max
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'matches',
    indexes: [
      {
        fields: ['teacher_id']
      },
      {
        fields: ['learner_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['scheduled_date']
      },
      {
        fields: ['teacher_skill_id']
      },
      {
        fields: ['learner_skill_id']
      }
    ],
    validate: {
      teacherNotLearner() {
        if (this.teacher_id === this.learner_id) {
          throw new Error('Teacher and learner cannot be the same user');
        }
      }
    }
  });

  return Match;
};
