const Joi = require('joi');

const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        success: false,
        error: errorMessage
      });
    }
    
    next();
  };
};

// Validation schemas
const registerSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(50).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  name: Joi.string().min(2).max(100).required(),
  bio: Joi.string().max(1000).optional(),
  current_status: Joi.string().max(50).optional(),
  state: Joi.string().max(50).optional(),
  country: Joi.string().max(50).optional(),
  education_level: Joi.string().max(50).optional(),
  university_name: Joi.string().max(100).optional()
});

const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

const updateProfileSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  bio: Joi.string().max(1000).optional(),
  current_status: Joi.string().max(50).optional(),
  state: Joi.string().max(50).optional(),
  country: Joi.string().max(50).optional(),
  education_level: Joi.string().max(50).optional(),
  university_name: Joi.string().max(100).optional()
});

const skillSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  category: Joi.string().valid('Technology', 'Business', 'Creative', 'Language', 'Health', 'Education', 'Sports', 'Music', 'Cooking', 'Other').required(),
  description: Joi.string().max(1000).optional(),
  requires_certification: Joi.boolean().optional()
});

const offeredSkillSchema = Joi.object({
  skill_id: Joi.number().integer().positive().required(),
  proficiency_level: Joi.string().valid('intermediate', 'advanced', 'expert').required(),
  years_experience: Joi.number().integer().min(0).max(50).optional(),
  description: Joi.string().max(1000).optional(),
  has_certification: Joi.boolean().optional()
});

const desiredSkillSchema = Joi.object({
  skill_id: Joi.number().integer().positive().required(),
  interest_level: Joi.string().valid('low', 'medium', 'high').required(),
  current_knowledge: Joi.string().valid('none', 'beginner', 'some').optional(),
  learning_goal: Joi.string().max(1000).optional()
});

const matchSchema = Joi.object({
  teacher_skill_id: Joi.number().integer().positive().required(),
  learner_skill_id: Joi.number().integer().positive().required(),
  scheduled_date: Joi.date().optional(),
  duration_minutes: Joi.number().integer().min(15).max(480).optional(),
  notes: Joi.string().max(1000).optional()
});

module.exports = {
  validate,
  registerSchema,
  loginSchema,
  updateProfileSchema,
  skillSchema,
  offeredSkillSchema,
  desiredSkillSchema,
  matchSchema
};
