const { User, UserOfferedSkill, UserDesired<PERSON>kill, Skill } = require('../config/database');
const { Op } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     UserProfile:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         username:
 *           type: string
 *         email:
 *           type: string
 *         name:
 *           type: string
 *         avatar:
 *           type: string
 *         bio:
 *           type: string
 *         current_status:
 *           type: string
 *         state:
 *           type: string
 *         country:
 *           type: string
 *         education_level:
 *           type: string
 *         university_name:
 *           type: string
 *         status:
 *           type: string
 *         is_admin:
 *           type: boolean
 *         offeredSkills:
 *           type: array
 *           items:
 *             type: object
 *         desiredSkills:
 *           type: array
 *           items:
 *             type: object
 */

// Get all users with pagination and search
const getAllUsers = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      status = 'active',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {
      status: status === 'all' ? { [Op.ne]: null } : status
    };

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { username: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows: users } = await User.findAndCountAll({
      where: whereClause,
      include: [
        {
          association: 'offeredSkills',
          include: ['skill'],
          where: { status: 'approved' },
          required: false
        },
        {
          association: 'desiredSkills',
          include: ['skill'],
          where: { status: 'active' },
          required: false
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalUsers: count,
          hasNext: page * limit < count,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching users'
    });
  }
};

// Get user by ID
const getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      include: [
        {
          association: 'offeredSkills',
          include: ['skill'],
          where: { status: 'approved' },
          required: false
        },
        {
          association: 'desiredSkills',
          include: ['skill'],
          where: { status: 'active' },
          required: false
        }
      ]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching user'
    });
  }
};

// Update user profile
const updateUserProfile = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      bio,
      current_status,
      state,
      country,
      education_level,
      university_name,
      avatar
    } = req.body;

    // Check if user exists
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Check authorization (users can only update their own profile, admins can update any)
    if (req.user.id !== parseInt(id) && !req.user.is_admin) {
      return res.status(403).json({
        success: false,
        error: 'Access denied. You can only update your own profile.'
      });
    }

    // Update user
    await user.update({
      name: name || user.name,
      bio: bio !== undefined ? bio : user.bio,
      current_status: current_status || user.current_status,
      state: state || user.state,
      country: country || user.country,
      education_level: education_level || user.education_level,
      university_name: university_name || user.university_name,
      avatar: avatar || user.avatar
    });

    // Fetch updated user with associations
    const updatedUser = await User.findByPk(id, {
      include: [
        {
          association: 'offeredSkills',
          include: ['skill']
        },
        {
          association: 'desiredSkills',
          include: ['skill']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user: updatedUser }
    });
  } catch (error) {
    console.error('Update user profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating profile'
    });
  }
};

// Get user's offered skills
const getUserOfferedSkills = async (req, res) => {
  try {
    const { id } = req.params;

    const offeredSkills = await UserOfferedSkill.findAll({
      where: { user_id: id },
      include: ['skill', 'user'],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: { offeredSkills }
    });
  } catch (error) {
    console.error('Get user offered skills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching offered skills'
    });
  }
};

// Add offered skill
const addOfferedSkill = async (req, res) => {
  try {
    const { id } = req.params;
    const { skill_id, proficiency_level, years_experience, description, has_certification } = req.body;

    // Check authorization
    if (req.user.id !== parseInt(id) && !req.user.is_admin) {
      return res.status(403).json({
        success: false,
        error: 'Access denied. You can only manage your own skills.'
      });
    }

    // Check if skill already exists for user
    const existingSkill = await UserOfferedSkill.findOne({
      where: { user_id: id, skill_id }
    });

    if (existingSkill) {
      return res.status(400).json({
        success: false,
        error: 'You have already added this skill'
      });
    }

    const offeredSkill = await UserOfferedSkill.create({
      user_id: id,
      skill_id,
      proficiency_level,
      years_experience,
      description,
      has_certification: has_certification || false,
      status: 'pending' // Requires admin approval
    });

    const skillWithDetails = await UserOfferedSkill.findByPk(offeredSkill.id, {
      include: ['skill', 'user']
    });

    res.status(201).json({
      success: true,
      message: 'Skill added successfully. Pending admin approval.',
      data: { offeredSkill: skillWithDetails }
    });
  } catch (error) {
    console.error('Add offered skill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while adding skill'
    });
  }
};

// Get user's desired skills
const getUserDesiredSkills = async (req, res) => {
  try {
    const { id } = req.params;

    const desiredSkills = await UserDesiredSkill.findAll({
      where: { user_id: id },
      include: ['skill', 'user'],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: { desiredSkills }
    });
  } catch (error) {
    console.error('Get user desired skills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching desired skills'
    });
  }
};

// Add desired skill
const addDesiredSkill = async (req, res) => {
  try {
    const { id } = req.params;
    const { skill_id, interest_level, current_knowledge, learning_goal } = req.body;

    // Check authorization
    if (req.user.id !== parseInt(id) && !req.user.is_admin) {
      return res.status(403).json({
        success: false,
        error: 'Access denied. You can only manage your own skills.'
      });
    }

    // Check if skill already exists for user
    const existingSkill = await UserDesiredSkill.findOne({
      where: { user_id: id, skill_id }
    });

    if (existingSkill) {
      return res.status(400).json({
        success: false,
        error: 'You have already added this skill to your learning list'
      });
    }

    const desiredSkill = await UserDesiredSkill.create({
      user_id: id,
      skill_id,
      interest_level,
      current_knowledge,
      learning_goal,
      status: 'active'
    });

    const skillWithDetails = await UserDesiredSkill.findByPk(desiredSkill.id, {
      include: ['skill', 'user']
    });

    res.status(201).json({
      success: true,
      message: 'Skill added to learning list successfully.',
      data: { desiredSkill: skillWithDetails }
    });
  } catch (error) {
    console.error('Add desired skill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while adding skill'
    });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  updateUserProfile,
  getUserOfferedSkills,
  addOfferedSkill,
  getUserDesiredSkills,
  addDesiredSkill
};
