# SkillSwap API Documentation

## Overview

The SkillSwap API provides a comprehensive backend for a skill-sharing platform where users can offer to teach skills and find others to learn from. The API supports user management, skill cataloging, matching algorithms, and administrative functions.

## Base URL
```
http://localhost:5000/api
```

## Authentication

The API uses JWT (JSON Web Token) authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## API Endpoints Summary

### Authentication (`/auth`)
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user profile
- `POST /auth/logout` - User logout

### Users (`/users`)
- `GET /users` - Get all users (with pagination/search)
- `GET /users/:id` - Get user by ID
- `PUT /users/:id` - Update user profile
- `GET /users/:id/offered-skills` - Get user's offered skills
- `POST /users/:id/offered-skills` - Add offered skill
- `GET /users/:id/desired-skills` - Get user's desired skills
- `POST /users/:id/desired-skills` - Add desired skill

### Skills (`/skills`)
- `GET /skills` - Get all skills (with filtering)
- `GET /skills/search` - Advanced skill search
- `GET /skills/categories` - Get skill categories
- `GET /skills/:id` - Get skill by ID with statistics
- `POST /skills` - Create skill (admin only)
- `PUT /skills/:id` - Update skill (admin only)
- `DELETE /skills/:id` - Delete skill (admin only)

### Matches (`/matches`)
- `GET /matches` - Get user's matches
- `GET /matches/potential` - Find potential matches
- `POST /matches` - Create match request
- `PUT /matches/:id` - Update match status
- `DELETE /matches/:id` - Delete match

### Admin (`/admin`)
- `GET /admin/dashboard` - Dashboard statistics
- `GET /admin/users` - All users management
- `PUT /admin/users/:id/status` - Update user status
- `DELETE /admin/users/:id` - Delete user
- `GET /admin/skills/pending` - Pending skill approvals
- `PUT /admin/skills/:id/moderate` - Approve/reject skills
- `GET /admin/matches` - All matches oversight

### Upload (`/upload`)
- `POST /upload/avatar` - Upload user avatar
- `POST /upload/certification/:skillId` - Upload skill certification
- `DELETE /upload/:type/:filename` - Delete uploaded file

## Data Models

### User
```json
{
  "id": 1,
  "username": "johndoe",
  "email": "<EMAIL>",
  "name": "John Doe",
  "avatar": "/uploads/avatars/avatar-123.jpg",
  "bio": "Software developer passionate about teaching",
  "current_status": "Software Engineer",
  "state": "California",
  "country": "USA",
  "education_level": "Bachelor's",
  "university_name": "Stanford University",
  "status": "active",
  "is_admin": false,
  "created_at": "2024-01-01T00:00:00.000Z",
  "updated_at": "2024-01-01T00:00:00.000Z"
}
```

### Skill
```json
{
  "id": 1,
  "name": "JavaScript",
  "category": "Technology",
  "description": "Programming language for web development",
  "requires_certification": false,
  "created_at": "2024-01-01T00:00:00.000Z",
  "updated_at": "2024-01-01T00:00:00.000Z"
}
```

### User Offered Skill
```json
{
  "id": 1,
  "user_id": 1,
  "skill_id": 1,
  "proficiency_level": "advanced",
  "years_experience": 5,
  "description": "5 years of full-stack JavaScript development",
  "has_certification": true,
  "certification_file": "/uploads/certifications/cert-123.pdf",
  "status": "approved"
}
```

### User Desired Skill
```json
{
  "id": 1,
  "user_id": 2,
  "skill_id": 1,
  "interest_level": "high",
  "current_knowledge": "beginner",
  "learning_goal": "Want to build web applications",
  "status": "active"
}
```

### Match
```json
{
  "id": 1,
  "teacher_skill_id": 1,
  "learner_skill_id": 1,
  "teacher_id": 1,
  "learner_id": 2,
  "status": "pending",
  "scheduled_date": "2024-01-15T14:00:00.000Z",
  "duration_minutes": 60,
  "notes": "Looking forward to learning JavaScript basics"
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

## Error Response Format

```json
{
  "success": false,
  "error": "Error message description"
}
```

## Success Response Format

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  }
}
```

## Pagination

List endpoints support pagination with the following query parameters:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10-20 depending on endpoint)
- `search` - Search query
- `sortBy` - Field to sort by
- `sortOrder` - ASC or DESC

Pagination response includes:
```json
{
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 50,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## File Upload

### Supported File Types
- **Avatars**: Image files (JPEG, PNG, GIF, WebP)
- **Certifications**: Image files and PDFs

### File Size Limits
- Maximum file size: 5MB
- Single file upload only

### Upload Response
```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "filePath": "/uploads/avatars/avatar-123.jpg",
    "updatedRecord": { /* Updated user/skill object */ }
  }
}
```

## Interactive Documentation

Visit `http://localhost:5000/api-docs` for interactive Swagger documentation where you can test all endpoints directly in your browser.

## Rate Limiting

- 100 requests per 15 minutes per IP address
- Rate limits apply to all `/api/*` endpoints

## CORS

The API supports CORS for frontend integration. Configure the `FRONTEND_URL` environment variable to set allowed origins.

## Environment Variables

See `.env.example` for all required environment variables including database configuration, JWT secrets, and file upload settings.
