const express = require('express');
const router = express.Router();

const {
  getAllSkills,
  getSkillById,
  getSkillCategories,
  createSkill,
  updateSkill,
  deleteSkill,
  searchSkills
} = require('../controllers/skillController');

const { authenticate } = require('../middleware/auth');
const { requireAdmin } = require('../middleware/admin');
const { validate, skillSchema } = require('../middleware/validation');

/**
 * @swagger
 * tags:
 *   name: Skills
 *   description: Skills management endpoints
 */

/**
 * @swagger
 * /skills:
 *   get:
 *     summary: Get all skills with pagination and filtering
 *     tags: [Skills]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: name
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *     responses:
 *       200:
 *         description: Skills retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/', getAllSkills);

/**
 * @swagger
 * /skills/search:
 *   get:
 *     summary: Search skills with advanced filtering
 *     tags: [Skills]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: hasTeachers
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: hasLearners
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Skills search results
 *       500:
 *         description: Server error
 */
router.get('/search', searchSkills);

/**
 * @swagger
 * /skills/categories:
 *   get:
 *     summary: Get skill categories with counts
 *     tags: [Skills]
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/categories', getSkillCategories);

/**
 * @swagger
 * /skills:
 *   post:
 *     summary: Create new skill (admin only)
 *     tags: [Skills]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - category
 *             properties:
 *               name:
 *                 type: string
 *               category:
 *                 type: string
 *               description:
 *                 type: string
 *               requires_certification:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Skill created successfully
 *       400:
 *         description: Skill already exists or validation error
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Server error
 */
router.post('/', authenticate, requireAdmin, validate(skillSchema), createSkill);

/**
 * @swagger
 * /skills/{id}:
 *   get:
 *     summary: Get skill by ID with statistics
 *     tags: [Skills]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Skill retrieved successfully
 *       404:
 *         description: Skill not found
 *       500:
 *         description: Server error
 */
router.get('/:id', getSkillById);

/**
 * @swagger
 * /skills/{id}:
 *   put:
 *     summary: Update skill (admin only)
 *     tags: [Skills]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               category:
 *                 type: string
 *               description:
 *                 type: string
 *               requires_certification:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Skill updated successfully
 *       400:
 *         description: Validation error or name conflict
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Skill not found
 *       500:
 *         description: Server error
 */
router.put('/:id', authenticate, requireAdmin, validate(skillSchema), updateSkill);

/**
 * @swagger
 * /skills/{id}:
 *   delete:
 *     summary: Delete skill (admin only)
 *     tags: [Skills]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Skill deleted successfully
 *       400:
 *         description: Skill is being used by users
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Skill not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', authenticate, requireAdmin, deleteSkill);

module.exports = router;
